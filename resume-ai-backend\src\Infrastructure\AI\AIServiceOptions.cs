namespace Infrastructure.AI;

public sealed class AIServiceOptions
{
    public const string SectionName = "AIService";

    public string Endpoint { get; set; } = string.Empty;
    public string ApiKey { get; set; } = string.Empty;
    public string Model { get; set; } = "deepseek-chat";
    public int MaxTokens { get; set; } = 4000;
    public double Temperature { get; set; } = 0.3;
    public int TimeoutSeconds { get; set; } = 600;
    public int MaxRetries { get; set; } = 3;
    public int RetryDelaySeconds { get; set; } = 2;
}
