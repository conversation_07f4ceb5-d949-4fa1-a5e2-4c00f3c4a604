using Application.Abstractions.Authentication;
using Application.Abstractions.Data;
using Application.Abstractions.Messaging;
using Domain.Resumes;
using Domain.Users;
using Microsoft.EntityFrameworkCore;
using SharedKernel;

namespace Application.Resumes.GetById;

internal sealed class GetResumeByIdQueryHandler(
    IApplicationDbContext context,
    IUserContext userContext)
    : IQueryHandler<GetResumeByIdQuery, ResumeResponse>
{
    public async Task<Result<ResumeResponse>> Handle(GetResumeByIdQuery query, CancellationToken cancellationToken)
    {
        // Build query with conditional content selection for performance
        IQueryable<Resume> resumeQuery = context.Resumes.AsNoTracking();

        Resume? resume;
        
        if (query.IncludeContent)
        {
            resume = await resumeQuery
                .FirstOrDefaultAsync(r => r.Id == query.ResumeId && !r.IsDeleted, cancellationToken);
        }
        else
        {
            // Exclude content for list views and metadata-only queries
            resume = await resumeQuery
                .Select(r => new Resume
                {
                    Id = r.Id,
                    UserId = r.UserId,
                    ParentId = r.ParentId,
                    ResumeContent = null!, // Exclude content for performance
                    CreatedAt = r.CreatedAt,
                    LastModifiedAt = r.LastModifiedAt,
                    IsDeleted = r.IsDeleted
                })
                .FirstOrDefaultAsync(r => r.Id == query.ResumeId && !r.IsDeleted, cancellationToken);
        }

        if (resume is null)
        {
            return Result.Failure<ResumeResponse>(ResumeErrors.NotFound(query.ResumeId));
        }

        // Authorization check - user can only access their own resumes
        if (userContext.UserId != resume.UserId)
        {
            return Result.Failure<ResumeResponse>(ResumeErrors.UnauthorizedAccess(query.ResumeId, userContext.UserId));
        }

        var response = new ResumeResponse(
            resume.Id,
            resume.UserId,
            resume.ParentId,
            query.IncludeContent ? resume.ResumeContent : null,
            resume.CreatedAt,
            resume.LastModifiedAt);

        return response;
    }
}
