<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON> - Resume</title>
</head>
<body>
    <div class="container">
        <header>
            <h1><PERSON></h1>
            <p>Senior Software Engineer</p>
            <p>Email: <EMAIL> | Phone: (*************</p>
        </header>

        <section class="experience">
            <h2>Professional Experience</h2>
            
            <div class="job">
                <h3>Senior Software Engineer</h3>
                <p><strong>Tech Company Inc.</strong> | 2020 - Present</p>
                <ul>
                    <li>Led development of microservices architecture</li>
                    <li>Mentored junior developers</li>
                    <li>Improved system performance by 40%</li>
                </ul>
            </div>

            <div class="job">
                <h3>Software Engineer</h3>
                <p><strong>StartUp Co.</strong> | 2018 - 2020</p>
                <ul>
                    <li>Developed full-stack web applications</li>
                    <li>Implemented CI/CD pipelines</li>
                    <li>Collaborated with cross-functional teams</li>
                </ul>
            </div>
        </section>

        <section class="education">
            <h2>Education</h2>
            <div class="degree">
                <h3>Bachelor of Science in Computer Science</h3>
                <p><strong>University of Technology</strong> | 2014 - 2018</p>
            </div>
        </section>

        <section class="skills">
            <h2>Technical Skills</h2>
            <ul>
                <li>Programming Languages: C#, JavaScript, Python, TypeScript</li>
                <li>Frameworks: .NET, React, Node.js, Angular</li>
                <li>Databases: SQL Server, PostgreSQL, MongoDB</li>
                <li>Cloud: Azure, AWS</li>
                <li>Tools: Docker, Kubernetes, Git, Jenkins</li>
            </ul>
        </section>
    </div>
</body>
</html>
