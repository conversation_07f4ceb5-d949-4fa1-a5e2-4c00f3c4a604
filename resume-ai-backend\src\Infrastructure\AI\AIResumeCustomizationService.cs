using System.Text.Json;
using Application.Abstractions.AI;
using Microsoft.Extensions.AI;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using SharedKernel;

namespace Infrastructure.AI;

internal sealed class AIResumeCustomizationService : IAIResumeCustomizationService
{
    private static readonly JsonSerializerOptions JsonOptions = new()
    {
        PropertyNameCaseInsensitive = true
    };

    private readonly IChatClient _chatClient;
    private readonly IPromptService _promptService;
    private readonly ILogger<AIResumeCustomizationService> _logger;
    private readonly AIServiceOptions _options;

    public AIResumeCustomizationService(
        IChatClient chatClient,
        IPromptService promptService,
        ILogger<AIResumeCustomizationService> logger,
        IOptions<AIServiceOptions> options)
    {
        _chatClient = chatClient;
        _promptService = promptService;
        _logger = logger;
        _options = options.Value;
    }

    public async Task<Result<AICustomizationResponse>> CustomizeResumeAsync(
        AICustomizationRequest request,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Starting AI resume customization for job: {JobTitle}", request.JobTitle);

            // Validate input
            if (string.IsNullOrWhiteSpace(request.OriginalResumeContent))
            {
                return Result.Failure<AICustomizationResponse>(AIServiceErrors.InvalidContent());
            }

            // Determine job category for specialized instructions
            _ = DetermineJobCategory(request.JobTitle, request.JobDescription);

            // Build chat messages using prompt service
            string systemMessage = _promptService.GetSystemMessage();
            string userPrompt = _promptService.GetUserPrompt(
                request.JobTitle,
                request.CompanyUrl,
                request.JobDescription,
                request.OriginalResumeContent);

            var messages = new List<ChatMessage>
            {
                new(ChatRole.System, systemMessage),
                new(ChatRole.User, userPrompt)
            };

            // Configure chat options
            var chatOptions = new ChatOptions
            {
                MaxOutputTokens = _options.MaxTokens,
                Temperature = (float)_options.Temperature,
                ResponseFormat = ChatResponseFormat.Json
            };

            _logger.LogDebug("Sending request to Ollama with {MessageCount} messages", messages.Count);

            // Call Ollama through IChatClient
            ChatResponse response = await _chatClient.GetResponseAsync(
                messages,
                chatOptions,
                cancellationToken);

            if (response.Text is null)
            {
                _logger.LogError("Ollama returned empty response");
                return Result.Failure<AICustomizationResponse>(AIServiceErrors.InvalidResponse());
            }

            // Parse the AI response
            AICustomizationResponse customizationResponse = ParseAIResponse(response.Text);

            // Validate response quality
            QualityControlConfig qualityControl = _promptService.GetConfiguration().QualityControl;
            if (customizationResponse.ConfidenceScore < qualityControl.MinConfidenceThreshold)
            {
                _logger.LogWarning("AI customization confidence {Confidence} below threshold {Threshold}",
                    customizationResponse.ConfidenceScore, qualityControl.MinConfidenceThreshold);
            }

            if (customizationResponse.CustomizedResumeContent.Length > qualityControl.MaxContentLength)
            {
                _logger.LogWarning("Customized content length {Length} exceeds maximum {MaxLength}",
                    customizationResponse.CustomizedResumeContent.Length, qualityControl.MaxContentLength);

                return Result.Failure<AICustomizationResponse>(AIServiceErrors.ProcessingFailed("Content too long"));
            }

            _logger.LogInformation("AI resume customization completed successfully with confidence {Confidence}",
                customizationResponse.ConfidenceScore);

            return Result.Success(customizationResponse);
        }
        catch (OperationCanceledException)
        {
            _logger.LogWarning("AI service request was cancelled or timed out");
            return Result.Failure<AICustomizationResponse>(AIServiceErrors.Timeout());
        }
        catch (JsonException ex)
        {
            _logger.LogError(ex, "Failed to parse AI service response as JSON");
            return Result.Failure<AICustomizationResponse>(AIServiceErrors.InvalidResponse());
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unexpected error occurred during AI processing");
            return Result.Failure<AICustomizationResponse>(AIServiceErrors.ProcessingFailed(ex.Message));
        }
    }

    private static string? DetermineJobCategory(string jobTitle, string jobDescription)
    {
        string content = $"{jobTitle} {jobDescription}".ToUpperInvariant();

        if (content.Contains("software") || content.Contains("developer") || content.Contains("engineer") ||
            content.Contains("programming") || content.Contains("technical"))
        {
            return "technology";
        }

        if (content.Contains("marketing") || content.Contains("brand") || content.Contains("campaign") ||
            content.Contains("social media"))
        {
            return "marketing";
        }

        if (content.Contains("finance") || content.Contains("accounting") || content.Contains("financial") ||
            content.Contains("analyst"))
        {
            return "finance";
        }

        if (content.Contains("healthcare") || content.Contains("medical") || content.Contains("nurse") ||
            content.Contains("doctor") || content.Contains("clinical"))
        {
            return "healthcare";
        }

        return null; // Use default instructions
    }

    private static AICustomizationResponse ParseAIResponse(string aiResponse)
    {
        try
        {
            OllamaResponseJson? jsonResponse = JsonSerializer.Deserialize<OllamaResponseJson>(aiResponse, JsonOptions);

            return new AICustomizationResponse(
                jsonResponse?.CustomizedContent ?? aiResponse,
                jsonResponse?.Summary ?? "AI customization completed",
                jsonResponse?.Confidence ?? 0.7);
        }
        catch
        {
            // Fallback if JSON parsing fails
            return new AICustomizationResponse(
                aiResponse,
                "AI customization completed (fallback parsing)",
                0.6);
        }
    }

    private sealed class OllamaResponseJson
    {
        public string? CustomizedContent { get; init; } = string.Empty;
        public string? Summary { get; init; } = string.Empty;
        public double Confidence { get; init; }
    }
}

