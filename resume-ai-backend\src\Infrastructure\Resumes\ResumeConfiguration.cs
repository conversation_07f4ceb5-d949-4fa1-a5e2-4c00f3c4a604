﻿using Domain.Resumes;
using Domain.Todos;
using Domain.Users;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Infrastructure.Resumes;

internal sealed class ResumeConfiguration : IEntityTypeConfiguration<Resume>
{
    public void Configure(EntityTypeBuilder<Resume> builder)
    {
        builder.HasKey(t => t.Id);

        // Configure ResumeContent for optimal storage
        builder.Property(t => t.ResumeContent)
            .HasColumnType("text") // Use TEXT type for large content
            .IsRequired();

        // Add index on UserId for faster queries
        builder.HasIndex(t => t.UserId)
            .HasDatabaseName("ix_resumes_user_id");

        // Add index on ParentId for faster parent-child queries
        builder.HasIndex(t => t.ParentId)
            .HasDatabaseName("ix_resumes_parent_id");

        // Foreign key relationships
        builder.HasOne<User>()
            .WithMany()
            .HasForeignKey(t => t.UserId)
            .OnDelete(DeleteBehavior.Cascade);

        // Configure optional self-referencing foreign key for parent resume
        builder.HasOne<Resume>()
            .WithMany()
            .HasForeignKey(t => t.ParentId)
            .IsRequired(false)
            .OnDelete(DeleteBehavior.Restrict);

        // Add unique constraint to ensure only one parent resume per user
        builder.HasIndex(t => t.UserId)
            .HasFilter("parent_id IS NULL")
            .IsUnique()
            .HasDatabaseName("ix_resumes_user_id_parent_unique");
    }
}
