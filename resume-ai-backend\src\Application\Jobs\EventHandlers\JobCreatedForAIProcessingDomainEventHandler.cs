using Application.Abstractions.AI;
using Application.Abstractions.Data;
using Application.Abstractions.Messaging;
using Application.JobApplications.CreateApplication;
using Application.Resumes.CreateCustomized;
using Domain.JobApplications;
using Domain.Jobs;
using Domain.Resumes;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using SharedKernel;

namespace Application.Jobs.EventHandlers;

internal sealed class JobCreatedForAIProcessingDomainEventHandler(
    IApplicationDbContext context,
    IAIResumeCustomizationService aiService,
    ICommandHandler<CreateCustomizedResumeCommand, Guid> createResumeHandler,
    ICommandHandler<CreateJobApplicationCommand, Guid> createApplicationHandler,
    ILogger<JobCreatedForAIProcessingDomainEventHandler> logger)
    : IDomainEventHandler<JobCreatedForAIProcessingDomainEvent>
{
    public async Task Handle(JobCreatedForAIProcessingDomainEvent domainEvent, CancellationToken cancellationToken)
    {
        try
        {
            logger.LogInformation("Processing AI customization for Job {JobId}, User {UserId}, Resume {ResumeId}",
                domainEvent.JobId, domainEvent.UserId, domainEvent.ParentResumeId);

            // 1. Retrieve job details
            Job? job = await context.Jobs
                .AsNoTracking()
                .FirstOrDefaultAsync(j => j.Id == domainEvent.JobId && !j.IsDeleted, cancellationToken);

            if (job is null)
            {
                logger.LogWarning("Job {JobId} not found for AI processing", domainEvent.JobId);
                return;
            }

            // 2. Retrieve parent resume content
            Resume? parentResume = await context.Resumes
                .AsNoTracking()
                .FirstOrDefaultAsync(r => r.Id == domainEvent.ParentResumeId && !r.IsDeleted, cancellationToken);

            if (parentResume is null)
            {
                logger.LogWarning("Parent resume {ResumeId} not found for AI processing", domainEvent.ParentResumeId);
                return;
            }

            // 3. Call AI service for customization
            var aiRequest = new AICustomizationRequest(
                job.JobTitle,
                job.JobDescription,
                job.CompanyUrl,
                parentResume.ResumeContent);

            Result<AICustomizationResponse> aiResult = await aiService.CustomizeResumeAsync(aiRequest, cancellationToken);

            if (aiResult.IsFailure)
            {
                logger.LogError("AI customization failed for Job {JobId}: {Error}", 
                    domainEvent.JobId, aiResult.Error.Description);
                return;
            }

            AICustomizationResponse aiResponse = aiResult.Value;

            // 4. Create customized resume
            var createResumeCommand = new CreateCustomizedResumeCommand(
                domainEvent.UserId,
                domainEvent.ParentResumeId,
                aiResponse.CustomizedResumeContent,
                aiResponse.CustomizationSummary);

            Result<Guid> resumeResult = await createResumeHandler.Handle(createResumeCommand, cancellationToken);

            if (resumeResult.IsFailure)
            {
                logger.LogError("Failed to create customized resume for Job {JobId}: {Error}",
                    domainEvent.JobId, resumeResult.Error.Description);
                return;
            }

            Guid customizedResumeId = resumeResult.Value;

            // 5. Create job application
            var createApplicationCommand = new CreateJobApplicationCommand(
                customizedResumeId,
                domainEvent.JobId,
                domainEvent.UserId);

            Result<Guid> applicationResult = await createApplicationHandler.Handle(createApplicationCommand, cancellationToken);

            if (applicationResult.IsFailure)
            {
                logger.LogError("Failed to create job application for Job {JobId}: {Error}",
                    domainEvent.JobId, applicationResult.Error.Description);
                return;
            }

            logger.LogInformation("Successfully completed AI processing for Job {JobId}. " +
                "Created resume {ResumeId} and application {ApplicationId}",
                domainEvent.JobId, customizedResumeId, applicationResult.Value);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Unexpected error during AI processing for Job {JobId}", domainEvent.JobId);
        }
    }
}
