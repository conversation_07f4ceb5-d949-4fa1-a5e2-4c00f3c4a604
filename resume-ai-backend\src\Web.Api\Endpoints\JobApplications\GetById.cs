using Application.Abstractions.Messaging;
using Application.JobApplications.GetById;
using SharedKernel;
using Web.Api.Extensions;
using Web.Api.Infrastructure;

namespace Web.Api.Endpoints.JobApplications;

internal sealed class GetById : IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder app)
    {
        app.MapGet("job-applications/{id:guid}", async (
            Guid id,
            IQueryHandler<GetJobApplicationByIdQuery, JobApplicationResponse> handler,
            CancellationToken cancellationToken) =>
        {
            var query = new GetJobApplicationByIdQuery(id);

            Result<JobApplicationResponse> result = await handler.Handle(query, cancellationToken);

            return result.Match(Results.Ok, CustomResults.Problem);
        })
        .WithTags(Tags.JobApplications)
        .WithName("GetJobApplicationById")
        .Produces<JobApplicationResponse>()
        .ProducesValidationProblem()
        .RequireAuthorization();
    }
}
