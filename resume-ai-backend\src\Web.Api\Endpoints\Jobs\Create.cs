using Application.Abstractions.Messaging;
using Application.Jobs.Create;
using SharedKernel;
using Web.Api.Extensions;
using Web.Api.Infrastructure;

namespace Web.Api.Endpoints.Jobs;

internal sealed class Create : IEndpoint
{
    public sealed record CreateJobRequest(
        string JobTitle,
        string JobDescription,
        string JobPostingUrl,
        string CompanyUrl);

    public void MapEndpoint(IEndpointRouteBuilder app)
    {
        app.MapPost("jobs", async (
            CreateJobRequest request,
            ICommandHandler<CreateJobCommand, Guid> handler,
            CancellationToken cancellationToken) =>
        {
            var command = new CreateJobCommand(
                request.JobTitle,
                request.JobDescription,
                request.JobPostingUrl,
                request.CompanyUrl);

            Result<Guid> result = await handler.<PERSON>le(command, cancellationToken);

            return result.Match(
                jobId => Results.Created($"/jobs/{jobId}", jobId),
                CustomResults.Problem);
        })
        .WithTags(Tags.Jobs)
        .WithName("CreateJob")
        .Produces<Guid>(StatusCodes.Status201Created)
        .ProducesValidationProblem()
        .RequireAuthorization();
    }
}
