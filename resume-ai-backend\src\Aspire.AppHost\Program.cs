IDistributedApplicationBuilder builder = DistributedApplication.CreateBuilder(args);

IResourceBuilder<PostgresDatabaseResource> database = builder
    .AddPostgres("database")
    .WithPgAdmin()
    .WithImage("postgres:17")
    // .WithBindMount("../../.containers/db", "/var/lib/postgresql/data")
    .AddDatabase("resume-ai");

// Configure Ollama service with DeepSeek model
IResourceBuilder<OllamaResource> ollama =
    builder.AddOllama("ollama")
        .WithDataVolume()
        .WithOpenWebUI();

IResourceBuilder<OllamaModelResource> deepseekModel = ollama.AddModel("chat", "llama3");

builder.AddProject<Projects.Web_Api>("web-api")
    .WithEnvironment("ConnectionStrings__Database", database)
    .WithReference(database)
    .WithReference(deepseekModel)
    .WaitFor(database)
    .WaitFor(ollama);

builder.Build().Run();
