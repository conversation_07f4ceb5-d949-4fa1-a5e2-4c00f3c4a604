using Application.Services;

namespace Application.UnitTests.Services;

public class HtmlContentProcessorTests
{
    private readonly HtmlContentProcessor _processor = new();

    [Fact]
    public void ProcessHtmlContent_WithMultilineHtml_ShouldReturnSingleLine()
    {
        // Arrange
        const string multilineHtml = """
            <html>
            <body>

            <h1>My First Heading</h1>
            <p>My first paragraph.</p>

            </body>
            </html>
            """;

        const string expectedResult = "<html><body><h1>My First Heading</h1><p>My first paragraph.</p></body></html>";

        // Act
        string result = _processor.ProcessHtmlContent(multilineHtml);

        // Assert
        result.Should().Be(expectedResult);
    }

    [Fact]
    public void ProcessHtmlContent_WithExcessiveWhitespace_ShouldNormalizeWhitespace()
    {
        // Arrange
        const string htmlWithWhitespace = "<div>    <p>   Text   with   spaces   </p>    </div>";
        const string expectedResult = "<div><p> Text with spaces </p></div>";

        // Act
        string result = _processor.ProcessHtmlContent(htmlWithWhitespace);

        // Assert
        result.Should().Be(expectedResult);
    }

    [Fact]
    public void ProcessHtmlContent_WithEmptyString_ShouldReturnEmpty()
    {
        // Act
        string result = _processor.ProcessHtmlContent("");

        // Assert
        result.Should().BeEmpty();
    }

    [Fact]
    public void ProcessHtmlContent_WithWhitespaceOnly_ShouldReturnEmpty()
    {
        // Act
        string result = _processor.ProcessHtmlContent("   \n\r\t   ");

        // Assert
        result.Should().BeEmpty();
    }

    [Fact]
    public void ProcessHtmlContent_WithComplexHtml_ShouldPreserveStructure()
    {
        // Arrange
        const string complexHtml = """
            <!DOCTYPE html>
            <html lang="en">
            <head>
                <meta charset="UTF-8">
                <title>Resume</title>
            </head>
            <body>
                <div class="container">
                    <h1>John Doe</h1>
                    <p>Software Engineer</p>
                    <ul>
                        <li>Experience 1</li>
                        <li>Experience 2</li>
                    </ul>
                </div>
            </body>
            </html>
            """;

        // Act
        string result = _processor.ProcessHtmlContent(complexHtml);

        // Assert
        result.Should().NotContain("\n");
        result.Should().NotContain("\r");
        result.Should().Contain("<h1>John Doe</h1>");
        result.Should().Contain("<p>Software Engineer</p>");
        result.Should().Contain("<li>Experience 1</li>");
    }

    [Theory]
    [InlineData("<p>Valid HTML</p>", true)]
    [InlineData("<div><span>Nested tags</span></div>", true)]
    [InlineData("Plain text without tags", false)]
    [InlineData("", false)]
    [InlineData("   ", false)]
    [InlineData("<p>Unclosed tag", true)] // Basic validation allows this
    [InlineData("< >", true)] // Minimal valid structure
    public void IsValidHtml_ShouldValidateCorrectly(string html, bool expected)
    {
        // Act
        bool result = _processor.IsValidHtml(html);

        // Assert
        result.Should().Be(expected);
    }

    [Fact]
    public void ProcessHtmlContent_WithNewlinesAndTabs_ShouldNormalize()
    {
        // Arrange
        const string htmlWithNewlinesAndTabs = "<div>\n\t<p>\n\t\tContent\n\t</p>\n</div>";
        const string expectedResult = "<div><p> Content </p></div>";

        // Act
        string result = _processor.ProcessHtmlContent(htmlWithNewlinesAndTabs);

        // Assert
        result.Should().Be(expectedResult);
    }

    [Fact]
    public void ProcessHtmlContent_WithSelfClosingTags_ShouldPreserveTags()
    {
        // Arrange
        const string htmlWithSelfClosing = """
            <div>
                <img src="image.jpg" alt="Image" />
                <br />
                <input type="text" name="field" />
            </div>
            """;

        // Act
        string result = _processor.ProcessHtmlContent(htmlWithSelfClosing);

        // Assert
        result.Should().Contain("<img src=\"image.jpg\" alt=\"Image\" />");
        result.Should().Contain("<br />");
        result.Should().Contain("<input type=\"text\" name=\"field\" />");
        result.Should().NotContain("\n");
    }
}
