### Test HTML Content Processing
POST {{baseUrl}}/resumes
Content-Type: application/json
Authorization: Bearer {{token}}

{
  "userId": "0b2a3db7-00eb-4d2f-a555-f397371a0287",
  "resumeContent": "<html><body><h1>My First Heading</h1><p>My first paragraph.</p></body></html>"
}

### Test HTML Content with Newlines (This should now work)
POST {{baseUrl}}/resumes
Content-Type: application/json
Authorization: Bearer {{token}}

{
  "userId": "0b2a3db7-00eb-4d2f-a555-f397371a0287",
  "resumeContent": "<html>\n<body>\n\n<h1>My First Heading</h1>\n<p>My first paragraph.</p>\n\n</body>\n</html>"
}

### Test Complex HTML with Whitespace
POST {{baseUrl}}/resumes
Content-Type: application/json
Authorization: Bearer {{token}}

{
  "userId": "0b2a3db7-00eb-4d2f-a555-f397371a0287",
  "resumeContent": "<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n    <meta charset=\"UTF-8\">\n    <title>Resume</title>\n</head>\n<body>\n    <div class=\"container\">\n        <h1>John Doe</h1>\n        <p>Software Engineer</p>\n        <ul>\n            <li>Experience 1</li>\n            <li>Experience 2</li>\n        </ul>\n    </div>\n</body>\n</html>"
}
