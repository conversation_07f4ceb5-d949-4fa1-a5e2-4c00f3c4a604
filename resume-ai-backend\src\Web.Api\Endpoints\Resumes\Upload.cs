using Application.Abstractions.Messaging;
using Application.Resumes.Create;
using Application.Services;
using SharedKernel;
using Web.Api.Extensions;
using Web.Api.Infrastructure;

namespace Web.Api.Endpoints.Resumes;

internal sealed class Upload : IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder app)
    {
        app.MapPost("resumes/upload", UploadResumeAsync)
            .WithTags(Tags.Resumes)
            .WithName("UploadResume")
            .Produces<Guid>()
            .ProducesValidationProblem()
            .RequireAuthorization()
            .DisableAntiforgery(); // Required for file uploads
    }

    private static async Task<IResult> UploadResumeAsync(
        Guid userId,
        Guid? parentId,
        IFormFile file,
        ICommandHandler<CreateResumeCommand, Guid> handler,
        IHtmlContentProcessor htmlContentProcessor,
        CancellationToken cancellationToken)
    {
        
        (bool isSuccess, string processedContent) = await ExtractContentFromFileAsync(file, htmlContentProcessor, cancellationToken);

        if (!isSuccess)
        {
            return Results.BadRequest(processedContent);
        }

        // Create the command
        var command = new CreateResumeCommand
            {
                UserId = userId,
                ParentId = parentId,
                ResumeContent = processedContent
            };

        // Execute the command
        Result<Guid> result = await handler.Handle(command, cancellationToken);

        return result.Match(
            resumeId => Results.Created($"/resumes/{resumeId}", resumeId),
            CustomResults.Problem);
    }

    private static async Task<(bool IsSuccess, string Content)> ExtractContentFromFileAsync(
        IFormFile file,
        IHtmlContentProcessor htmlContentProcessor,
        CancellationToken cancellationToken)
    {
        if (file == null || file.Length == 0)
        {
            return (false, "No file uploaded or file is empty.");
        }

        // Validate file size (1MB limit)
        const int maxFileSize = 1_000_000;
        if (file.Length > maxFileSize)
        {
            return (false, $"File size exceeds the maximum limit of {maxFileSize:N0} bytes.");
        }

        // Validate file type
        string[] allowedContentTypes = ["text/html", "text/plain", "application/octet-stream"];
        if (!allowedContentTypes.Contains(file.ContentType))
        {
            return (false, $"Invalid file type. Allowed types: {string.Join(", ", allowedContentTypes)}");
        }

        // Validate file extension
        string[] allowedExtensions = [".HTML", ".HTM", ".TXT"];
        string fileExtension = Path.GetExtension(file.FileName).ToUpperInvariant();
        if (!allowedExtensions.Contains(fileExtension))
        {
            return (false, "Invalid file extension. Allowed extensions: .html, .htm, .txt");
        }

        try
        {
            // Read file content
            string htmlContent;
            using (var reader = new StreamReader(file.OpenReadStream()))
            {
                htmlContent = await reader.ReadToEndAsync(cancellationToken);
            }

            // Validate content is not empty
            if (string.IsNullOrWhiteSpace(htmlContent))
            {
                return (false, "File content is empty.");
            }

            // Process HTML content to normalize whitespace and format
            string processedContent = htmlContentProcessor.ProcessHtmlContent(htmlContent);

            // Validate processed content
            if (!htmlContentProcessor.IsValidHtml(processedContent))
            {
                return (false, "File does not contain valid HTML content.");
            }

            return (true, processedContent);
        }
        catch (Exception ex)
        {
            return (false,$"Error processing file: {ex.Message}");
        }// Process HTML content to normalize whitespace and format

    }
}
