using Application.Services;
using Microsoft.AspNetCore.Http;
using System.Text;

namespace Application.UnitTests.Endpoints;

public class CreateResumeFromFileTests
{
    private readonly HtmlContentProcessor _htmlProcessor = new();

    [Fact]
    public void ProcessHtmlContent_WithFileContent_ShouldNormalizeCorrectly()
    {
        // Arrange
        const string fileContent = """
            <html>
            <body>

            <h1>My First Heading</h1>

            <p>My first paragraph.</p>

            </body>
            </html>
            """;

        const string expectedResult = "<html><body><h1>My First Heading</h1><p>My first paragraph.</p></body></html>";

        // Act
        string result = _htmlProcessor.ProcessHtmlContent(fileContent);

        // Assert
        result.Should().Be(expectedResult);
    }

    [Fact]
    public void IsValidHtml_WithValidFileContent_ShouldReturnTrue()
    {
        // Arrange
        const string validHtml = "<html><body><h1>Title</h1><p>Content</p></body></html>";

        // Act
        bool result = _htmlProcessor.IsValidHtml(validHtml);

        // Assert
        result.Should().BeTrue();
    }

    [Fact]
    public void IsValidHtml_WithInvalidContent_ShouldReturnFalse()
    {
        // Arrange
        const string invalidContent = "This is just plain text without HTML tags";

        // Act
        bool result = _htmlProcessor.IsValidHtml(invalidContent);

        // Assert
        result.Should().BeFalse();
    }

    [Theory]
    [InlineData("resume.html", "text/html", true)]
    [InlineData("resume.htm", "text/html", true)]
    [InlineData("resume.txt", "text/plain", true)]
    [InlineData("resume.pdf", "application/pdf", false)]
    [InlineData("resume.docx", "application/vnd.openxmlformats-officedocument.wordprocessingml.document", false)]
    public void ValidateFileExtension_ShouldReturnExpectedResult(string fileName, string contentType, bool expectedValid)
    {
        // Arrange
        string[] allowedExtensions = [".HTML", ".HTM", ".TXT"];
        string[] allowedContentTypes = ["text/html", "text/plain", "application/octet-stream"];

        // Act
        string fileExtension = Path.GetExtension(fileName).ToUpperInvariant();
        bool isValidExtension = allowedExtensions.Contains(fileExtension);
        bool isValidContentType = allowedContentTypes.Contains(contentType);
        bool isValid = isValidExtension && isValidContentType;

        // Assert
        isValid.Should().Be(expectedValid);
    }

    [Fact]
    public void ProcessComplexHtmlContent_ShouldPreserveStructureAndNormalizeWhitespace()
    {
        // Arrange
        const string complexHtml = """
            <!DOCTYPE html>
            <html lang="en">
            <head>
                <meta charset="UTF-8">
                <title>Resume</title>
            </head>
            <body>
                <div class="container">
                    <h1>John Doe</h1>
                    <p>Software Engineer</p>
                    <ul>
                        <li>Experience 1</li>
                        <li>Experience 2</li>
                    </ul>
                </div>
            </body>
            </html>
            """;

        // Act
        string result = _htmlProcessor.ProcessHtmlContent(complexHtml);

        // Assert
        result.Should().NotContain("\n");
        result.Should().NotContain("\r");
        result.Should().Contain("<h1>John Doe</h1>");
        result.Should().Contain("<p>Software Engineer</p>");
        result.Should().Contain("<li>Experience 1</li>");
        result.Should().Contain("<li>Experience 2</li>");
        result.Should().StartWith("<!DOCTYPE html>");
        result.Should().EndWith("</html>");
    }
}
