### Test HTML File Upload
POST {{baseUrl}}/resumes/upload
Content-Type: multipart/form-data; boundary=----WebKitFormBoundary7MA4YWxkTrZu0gW
Authorization: Bearer {{token}}

------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="userId"

0b2a3db7-00eb-4d2f-a555-f397371a0287
------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="parentId"


------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="file"; filename="resume.html"
Content-Type: text/html

<html>
<body>

<h1>My First Heading</h1>

<p>My first paragraph.</p>

</body>
</html>
------WebKitFormBoundary7MA4YWxkTrZu0gW--

### Test Complex HTML File Upload
POST {{baseUrl}}/resumes/upload
Content-Type: multipart/form-data; boundary=----WebKitFormBoundary7MA4YWxkTrZu0gW
Authorization: Bearer {{token}}

------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="userId"

0b2a3db7-00eb-4d2f-a555-f397371a0287
------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="parentId"


------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="file"; filename="complex-resume.html"
Content-Type: text/html

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Resume</title>
</head>
<body>
    <div class="container">
        <h1>John Doe</h1>
        <p>Software Engineer</p>
        <ul>
            <li>Experience 1</li>
            <li>Experience 2</li>
        </ul>
    </div>
</body>
</html>
------WebKitFormBoundary7MA4YWxkTrZu0gW--
