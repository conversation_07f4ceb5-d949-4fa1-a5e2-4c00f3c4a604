using SharedKernel;

namespace Application.Abstractions.AI;

public static class AIServiceErrors
{
    public static Error ServiceUnavailable() => Error.Problem(
        "AIService.ServiceUnavailable",
        "The AI service is currently unavailable. Please try again later.");

    public static Error InvalidResponse() => Error.Problem(
        "AIService.InvalidResponse",
        "The AI service returned an invalid response.");

    public static Error ProcessingFailed(string reason) => Error.Problem(
        "AIService.ProcessingFailed",
        $"AI processing failed: {reason}");

    public static Error Timeout() => Error.Problem(
        "AIService.Timeout",
        "The AI service request timed out.");

    public static Error RateLimitExceeded() => Error.Problem(
        "AIService.RateLimitExceeded",
        "AI service rate limit exceeded. Please try again later.");

    public static Error InvalidContent() => Error.Problem(
        "AIService.InvalidContent",
        "The provided content is invalid or cannot be processed.");
}
