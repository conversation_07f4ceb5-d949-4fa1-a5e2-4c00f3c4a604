using Microsoft.Extensions.Logging;
using System.Reflection;
using YamlDotNet.Serialization;
using YamlDotNet.Serialization.NamingConventions;

namespace Infrastructure.AI;

public interface IPromptService
{
    string GetSystemMessage();
    string GetUserPrompt(string jobTitle, string companyUrl, string jobDescription, string originalResumeContent);
    string GetInstructions(string? jobCategory = null);
    string GetResponseFormat();
    PromptConfiguration GetConfiguration();
}

internal sealed class PromptService : IPromptService
{
    private readonly PromptConfiguration _configuration;
    private readonly ILogger<PromptService> _logger;

    public PromptService(ILogger<PromptService> logger)
    {
        _logger = logger;
        _configuration = LoadConfiguration();
    }

    public string GetSystemMessage()
    {
        return _configuration.Prompts.ResumeCustomization.SystemMessage;
    }

    public string GetUserPrompt(string jobTitle, string companyUrl, string jobDescription, string originalResumeContent)
    {
        string template = _configuration.Prompts.ResumeCustomization.UserPromptTemplate;
        string instructions = GetInstructions();
        string responseFormat = GetResponseFormat();

        return template
            .Replace("{job_title}", jobTitle)
            .Replace("{company_url}", companyUrl)
            .Replace("{job_description}", jobDescription)
            .Replace("{original_resume_content}", originalResumeContent)
            .Replace("{instructions}", instructions)
            .Replace("{response_format}", responseFormat);
    }

    public string GetInstructions(string? jobCategory = null)
    {
        string baseInstructions = _configuration.Prompts.ResumeCustomization.Instructions;
        
        if (!string.IsNullOrEmpty(jobCategory) &&
            _configuration.JobCategories.TryGetValue(jobCategory.ToUpperInvariant(), out JobCategoryConfig categoryConfig))
        {
            return $"{baseInstructions}\n\n**Additional Instructions for {jobCategory}:**\n{categoryConfig.AdditionalInstructions}";
        }

        return baseInstructions;
    }

    public string GetResponseFormat()
    {
        return _configuration.Prompts.ResumeCustomization.ResponseFormat;
    }

    public PromptConfiguration GetConfiguration()
    {
        return _configuration;
    }

    private PromptConfiguration LoadConfiguration()
    {
        try
        {
            var assembly = Assembly.GetExecutingAssembly();
            string resourceName = "Infrastructure.AI.Prompts.resume-customization-prompts.yaml";
            
            using Stream? stream = assembly.GetManifestResourceStream(resourceName);
            if (stream == null)
            {
                _logger.LogError("Could not find embedded resource: {ResourceName}", resourceName);
                return GetDefaultConfiguration();
            }

            using var reader = new StreamReader(stream);
            string yamlContent = reader.ReadToEnd();

            IDeserializer deserializer = new DeserializerBuilder()
                .WithNamingConvention(UnderscoredNamingConvention.Instance)
                .Build();

            PromptConfiguration configuration = deserializer.Deserialize<PromptConfiguration>(yamlContent);
            
            _logger.LogInformation("Successfully loaded AI prompt configuration");
            return configuration;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to load AI prompt configuration, using defaults");
            return GetDefaultConfiguration();
        }
    }

    private static PromptConfiguration GetDefaultConfiguration()
    {
        return new PromptConfiguration
        {
            Prompts = new PromptsSection
            {
                ResumeCustomization = new ResumeCustomizationPrompt
                {
                    SystemMessage = "You are an expert resume writer. Customize resumes to match job requirements while maintaining authenticity.",
                    UserPromptTemplate = "Customize this resume for the job: {job_title} at {company_url}. Job description: {job_description}. Resume: {original_resume_content}",
                    Instructions = "1. Highlight relevant skills\n2. Maintain factual information\n3. Use job keywords appropriately",
                    ResponseFormat = "Respond with JSON containing customizedContent, summary, and confidence fields."
                }
            },
            JobCategories = new Dictionary<string, JobCategoryConfig>(),
            QualityControl = new QualityControlConfig
            {
                MinConfidenceThreshold = 0.6,
                MaxContentLength = 50000,
                RequiredSections = new[] { "experience", "skills" },
                ValidationRules = new[] { "Maintain HTML structure", "No fictional content" }
            }
        };
    }
}

// Configuration classes
public sealed class PromptConfiguration
{
    public PromptsSection Prompts { get; set; } = new();
    public Dictionary<string, JobCategoryConfig> JobCategories { get; set; } = new();
    public QualityControlConfig QualityControl { get; set; } = new();
    public Dictionary<string, string> ErrorMessages { get; set; } = new();
}

public sealed class PromptsSection
{
    public ResumeCustomizationPrompt ResumeCustomization { get; set; } = new();
}

public sealed class ResumeCustomizationPrompt
{
    public string SystemMessage { get; set; } = string.Empty;
    public string UserPromptTemplate { get; set; } = string.Empty;
    public string Instructions { get; set; } = string.Empty;
    public string ResponseFormat { get; set; } = string.Empty;
    public string FallbackInstructions { get; set; } = string.Empty;
}

public sealed class JobCategoryConfig
{
    public string AdditionalInstructions { get; set; } = string.Empty;
}

public sealed class QualityControlConfig
{
    public double MinConfidenceThreshold { get; set; }
    public int MaxContentLength { get; set; }
    public string[] RequiredSections { get; set; } = Array.Empty<string>();
    public string[] ValidationRules { get; set; } = Array.Empty<string>();
}
