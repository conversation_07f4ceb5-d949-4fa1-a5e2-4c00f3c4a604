using Application.Abstractions.Messaging;
using Application.Resumes.Download;
using SharedKernel;
using Web.Api.Extensions;
using Web.Api.Infrastructure;

namespace Web.Api.Endpoints.Resumes;

internal sealed class Download : IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder app)
    {
        app.MapGet("resumes/{id:guid}/download", async (
            Guid id,
            IQueryHandler<DownloadResumeQuery, DownloadResumeResponse> handler,
            CancellationToken cancellationToken) =>
        {
            var query = new DownloadResumeQuery(id);

            Result<DownloadResumeResponse> result = await handler.Handle(query, cancellationToken);

            return result.Match(
                response => Results.File(
                    response.Content,
                    response.ContentType,
                    response.FileName),
                CustomResults.Problem);
        })
        .WithTags(Tags.Resumes)
        .WithName("DownloadResume")
        .Produces(StatusCodes.Status200OK, contentType: "text/html")
        .ProducesValidationProblem()
        .RequireAuthorization();
    }
}
