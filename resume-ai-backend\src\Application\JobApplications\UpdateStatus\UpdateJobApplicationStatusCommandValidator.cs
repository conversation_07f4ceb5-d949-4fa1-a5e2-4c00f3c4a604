using FluentValidation;

namespace Application.JobApplications.UpdateStatus;

public class UpdateJobApplicationStatusCommandValidator : AbstractValidator<UpdateJobApplicationStatusCommand>
{
    public UpdateJobApplicationStatusCommandValidator()
    {
        RuleFor(c => c.JobApplicationId)
            .NotEmpty()
            .WithMessage("Job ID is required");

        RuleFor(c => c.Status)
            .NotEmpty()
            .WithMessage("Status required")
            .IsInEnum();
    }
}
