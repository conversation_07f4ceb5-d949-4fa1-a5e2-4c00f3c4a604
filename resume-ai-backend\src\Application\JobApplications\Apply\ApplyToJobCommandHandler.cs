using System.ComponentModel;
using Application.Abstractions.Authentication;
using Application.Abstractions.Data;
using Application.Abstractions.Messaging;
using Domain.JobApplications;
using Domain.Jobs;
using Domain.Resumes;
using Microsoft.EntityFrameworkCore;
using SharedKernel;

namespace Application.JobApplications.Apply;

internal sealed class ApplyToJobCommandHandler(
    IApplicationDbContext context,
    IUserContext userContext,
    IDateTimeProvider dateTimeProvider)
    : ICommandHandler<ApplyToJobCommand, Guid>
{
    public async Task<Result<Guid>> Handle(ApplyToJobCommand command, CancellationToken cancellationToken)
    {
        // Check if the job exists
        Job? job = await context.Jobs
            .AsNoTracking()
            .FirstOrDefaultAsync(j => j.Id == command.JobId && !j.IsDeleted, cancellationToken);

        if (job is null)
        {
            return Result.Failure<Guid>(JobApplicationErrors.JobNotFound(command.JobId));
        }

        // Get the user's parent resume (the one with ParentId == null)
        Resume? resume = await context.Resumes
            .AsNoTracking()
            .FirstOrDefaultAsync(r => r.UserId == userContext.UserId && 
                                            r.Id == command.ResumeId && 
                                     !r.IsDeleted, cancellationToken);

        if (resume is null)
        {
            return Result.Failure<Guid>(JobApplicationErrors.ResumeNotFound(Guid.Empty));
        }

        // Check if the user has already applied to this job with any resume
        bool alreadyApplied = await context.JobApplications
            .AnyAsync(ja => ja.JobId == command.JobId && ja.CreatedBy == userContext.UserId, cancellationToken);

        if (alreadyApplied)
        {
            return Result.Failure<Guid>(JobApplicationErrors.AlreadyExists(resume.Id, command.JobId));
        }

        // Create the job application
        var jobApplication = JobApplication.Create(resume.Id, command.JobId, userContext.UserId);

        context.JobApplications.Add(jobApplication);

        job.AppliedAt = dateTimeProvider.UtcNow;
        context.Jobs.Update(job);

        await context.SaveChangesAsync(cancellationToken);

        return jobApplication.Id;
    }
}
