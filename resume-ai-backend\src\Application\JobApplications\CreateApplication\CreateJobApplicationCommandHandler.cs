using Application.Abstractions.Data;
using Application.Abstractions.Messaging;
using Domain.JobApplications;
using Domain.Jobs;
using Domain.Resumes;
using Microsoft.EntityFrameworkCore;
using SharedKernel;

namespace Application.JobApplications.CreateApplication;

internal sealed class CreateJobApplicationCommandHandler(
    IApplicationDbContext context)
    : ICommandHandler<CreateJobApplicationCommand, Guid>
{
    public async Task<Result<Guid>> Handle(CreateJobApplicationCommand command, CancellationToken cancellationToken)
    {
        // Validate job exists
        bool jobExists = await context.Jobs
            .AnyAsync(j => j.Id == command.JobId && !j.IsDeleted, cancellationToken);

        if (!jobExists)
        {
            return Result.Failure<Guid>(JobApplicationErrors.JobNotFound(command.JobId));
        }

        // Validate resume exists and belongs to user
        Resume? resume = await context.Resumes
            .AsNoTracking()
            .FirstOrDefaultAsync(r => r.Id == command.ResumeId && 
                                     r.UserId == command.CreatedBy && 
                                     !r.IsDeleted, cancellationToken);

        if (resume is null)
        {
            return Result.Failure<Guid>(JobApplicationErrors.ResumeNotFound(command.ResumeId));
        }

        // Check if application already exists for this resume-job combination
        bool applicationExists = await context.JobApplications
            .AnyAsync(ja => ja.ResumeId == command.ResumeId && ja.JobId == command.JobId, cancellationToken);

        if (applicationExists)
        {
            return Result.Failure<Guid>(JobApplicationErrors.AlreadyExists(command.ResumeId, command.JobId));
        }

        // Create the job application using domain factory method
        var jobApplication = JobApplication.Create(command.ResumeId, command.JobId, command.CreatedBy);

        context.JobApplications.Add(jobApplication);
        await context.SaveChangesAsync(cancellationToken);

        return jobApplication.Id;
    }
}
