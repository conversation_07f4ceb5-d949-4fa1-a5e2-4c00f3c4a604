using Application.Abstractions.Authentication;
using Application.Abstractions.Data;
using Application.Abstractions.Messaging;
using Domain.Jobs;
using Microsoft.EntityFrameworkCore;
using SharedKernel;

namespace Application.Jobs.Update;

internal sealed class UpdateJobCommandHandler(
    IApplicationDbContext context,
    IUserContext userContext,
    IDateTimeProvider dateTimeProvider)
    : ICommandHandler<UpdateJobCommand>
{
    public async Task<Result> Handle(UpdateJobCommand command, CancellationToken cancellationToken)
    {
        Job? job = await context.Jobs
            .FirstOrDefaultAsync(j => j.Id == command.JobId && !j.IsDeleted, cancellationToken);

        if (job is null)
        {
            return Result.Failure(JobErrors.NotFound(command.JobId));
        }

        // Authorization check - user can only update their own jobs
        if (userContext.UserId != job.UserId)
        {
            return Result.Failure(JobErrors.UnauthorizedAccess(command.JobId, userContext.UserId));
        }

        // Validate applied date is not in the future
        if (command.AppliedAt.HasValue && command.AppliedAt.Value > dateTimeProvider.UtcNow)
        {
            return Result.Failure(JobErrors.AppliedDateInFuture());
        }

        // Check for duplicate job (same title and company for the user, excluding current job)
        bool duplicateExists = await context.Jobs
            .AnyAsync(j => j.UserId == userContext.UserId &&
                          j.Id != command.JobId &&
                          j.JobTitle == command.JobTitle &&
                          j.CompanyUrl == command.CompanyUrl &&
                          !j.IsDeleted, cancellationToken);

        if (duplicateExists)
        {
            return Result.Failure(JobErrors.DuplicateJob(command.JobTitle, command.CompanyUrl));
        }

        // Update job properties to trigger audit
        job.JobTitle = command.JobTitle;
        job.JobDescription = command.JobDescription;
        job.JobPostingUrl = command.JobPostingUrl;
        job.CompanyUrl = command.CompanyUrl;
        job.AppliedAt = command.AppliedAt;

        await context.SaveChangesAsync(cancellationToken);

        return Result.Success();
    }
}
