using Application.Abstractions.Authentication;
using Application.Abstractions.Data;
using Application.Abstractions.Messaging;
using Domain.JobApplications;
using Microsoft.EntityFrameworkCore;
using SharedKernel;

namespace Application.JobApplications.UpdateStatus;

internal sealed class UpdateJobApplicationStatusCommandHandler(
    IApplicationDbContext context,
    IUserContext userContext)
    : ICommandHandler<UpdateJobApplicationStatusCommand>
{
    public async Task<Result> Handle(UpdateJobApplicationStatusCommand command, CancellationToken cancellationToken)
    {
        JobApplication? jobApplication = await context.JobApplications
            .FirstOrDefaultAsync(ja => ja.Id == command.JobApplicationId, cancellationToken);

        if (jobApplication is null)
        {
            return Result.Failure(JobApplicationErrors.NotFound(command.JobApplicationId));
        }

        // Authorization check - only the creator can update the status
        if (userContext.UserId != jobApplication.CreatedBy)
        {
            return Result.Failure(JobApplicationErrors.NotFound(command.JobApplicationId)); // Return NotFound for security
        }

        // Validate status transition (basic validation - can be extended)
        if (!IsValidStatusTransition(jobApplication.Status, command.Status))
        {
            return Result.Failure(JobApplicationErrors.InvalidStatusTransition(jobApplication.Status, command.Status));
        }

        // Update the status using the domain method
        jobApplication.UpdateStatus(command.Status);

        await context.SaveChangesAsync(cancellationToken);

        return Result.Success();
    }

    private static bool IsValidStatusTransition(JobApplicationStatus currentStatus, JobApplicationStatus newStatus)
    {
        // Basic validation - can be extended with more complex business rules
        if (currentStatus == newStatus)
        {
            return false; // No change needed
        }

        // Allow any transition for now - can be made more restrictive based on business rules
        // For example: Applied -> Interview -> (Accepted | Rejected)
        return true;
    }
}
