using Application.Abstractions.Authentication;
using Application.Abstractions.Data;
using Application.Abstractions.Messaging;
using Domain.Resumes;
using Microsoft.EntityFrameworkCore;
using SharedKernel;
using System.Text;

namespace Application.Resumes.Download;

internal sealed class DownloadResumeQueryHandler(
    IApplicationDbContext context,
    IUserContext userContext)
    : IQueryHandler<DownloadResumeQuery, DownloadResumeResponse>
{
    public async Task<Result<DownloadResumeResponse>> Handle(DownloadResumeQuery query, CancellationToken cancellationToken)
    {
        Resume? resume = await context.Resumes
            .AsNoTracking()
            .FirstOrDefaultAsync(r => r.Id == query.ResumeId && !r.IsDeleted, cancellationToken);

        if (resume is null)
        {
            return Result.Failure<DownloadResumeResponse>(ResumeErrors.NotFound(query.ResumeId));
        }

        // Authorization check - user can only download their own resumes
        if (userContext.UserId != resume.UserId)
        {
            return Result.Failure<DownloadResumeResponse>(ResumeErrors.UnauthorizedAccess(query.ResumeId, userContext.UserId));
        }

        // Create a complete HTML document with proper structure
        //string htmlContent = CreateCompleteHtmlDocument(resume.ResumeContent);
        
        // Convert HTML content to bytes
        byte[] contentBytes = Encoding.UTF8.GetBytes(resume.ResumeContent);
        
        // Generate filename with timestamp to avoid conflicts
        string fileName = $"resume_{resume.Id:N}_{DateTime.UtcNow:yyyyMMdd_HHmmss}.html";

        var response = new DownloadResumeResponse(
            fileName,
            "text/html",
            contentBytes);

        return response;
    }

//    private static string CreateCompleteHtmlDocument(string resumeContent)
//    {
//        // Create a complete HTML document with proper DOCTYPE, head, and styling
//        return $@"<!DOCTYPE html>
//<html lang=""en"">
//<head>
//    <meta charset=""UTF-8"">
//    <meta name=""viewport"" content=""width=device-width, initial-scale=1.0"">
//    <title>Resume</title>
//    <style>
//        body {{
//            font-family: Arial, sans-serif;
//            line-height: 1.6;
//            color: #333;
//            max-width: 800px;
//            margin: 0 auto;
//            padding: 20px;
//            background-color: #fff;
//        }}
//        .resume-container {{
//            background: white;
//            padding: 30px;
//            border-radius: 8px;
//            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
//        }}
//        h1, h2, h3 {{
//            color: #2c3e50;
//            margin-top: 0;
//        }}
//        h1 {{
//            border-bottom: 2px solid #3498db;
//            padding-bottom: 10px;
//        }}
//        .section {{
//            margin-bottom: 25px;
//        }}
//        @media print {{
//            body {{
//                margin: 0;
//                padding: 0;
//            }}
//            .resume-container {{
//                box-shadow: none;
//                border-radius: 0;
//            }}
//        }}
//    </style>
//</head>
//<body>
//    <div class=""resume-container"">
//        {resumeContent}
//    </div>
//</body>
//</html>";
//    }
}
