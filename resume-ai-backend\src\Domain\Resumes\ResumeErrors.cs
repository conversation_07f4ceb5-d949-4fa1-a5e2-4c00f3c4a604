﻿using SharedKernel;

namespace Domain.Resumes;

public static class ResumeErrors
{
    public static Error NotFound(Guid resumeId) => Error.NotFound(
        "Resumes.NotFound",
        $"The resume with the Id = '{resumeId}' was not found");

    public static Error UserNotFound(Guid userId) => Error.NotFound(
        "Resumes.UserNotFound",
        $"The user with Id = '{userId}' was not found");

    public static Error ParentResumeNotFound(Guid parentId) => Error.NotFound(
        "Resumes.ParentResumeNotFound",
        $"The parent resume with Id = '{parentId}' was not found");

    public static Error EmptyContent() => Error.Problem(
        "Resumes.EmptyContent",
        "Resume content cannot be empty or null");

    public static Error InvalidContent() => Error.Problem(
        "Resumes.InvalidContent",
        "Resume content format is invalid");

    public static Error UnauthorizedAccess(Guid resumeId, Guid userId) => Error.Problem(
        "Resumes.UnauthorizedAccess",
        $"User '{userId}' is not authorized to access resume '{resumeId}'");

    public static Error CannotDeleteResumeWithApplications(Guid resumeId) => Error.Problem(
        "Resumes.CannotDeleteResumeWithApplications",
        $"Cannot delete resume '{resumeId}' because it has associated job applications");

    public static Error InvalidParentResume(Guid resumeId, Guid parentId) => Error.Problem(
        "Resumes.InvalidParentResume",
        $"Resume '{resumeId}' cannot have itself or a descendant as parent '{parentId}'");

    public static Error MaxVersionsExceeded(Guid parentId, int maxVersions) => Error.Problem(
        "Resumes.MaxVersionsExceeded",
        $"Maximum number of resume versions ({maxVersions}) exceeded for parent resume '{parentId}'");

    public static Error UserAlreadyHasParentResume(Guid userId) => Error.Problem(
        "Resumes.UserAlreadyHasParentResume",
        $"User '{userId}' already has a parent resume. Only one parent resume is allowed per user");

    public static Error MustHaveParentResume(Guid userId) => Error.Problem(
        "Resumes.MustHaveParentResume",
        $"User '{userId}' must create a parent resume first before creating resume variations");
}
