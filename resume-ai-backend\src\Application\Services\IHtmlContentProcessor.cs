namespace Application.Services;

/// <summary>
/// Service for processing and normalizing HTML content
/// </summary>
public interface IHtmlContentProcessor
{
    /// <summary>
    /// Processes HTML content by removing unnecessary whitespace and normalizing format
    /// </summary>
    /// <param name="htmlContent">Raw HTML content that may contain newlines and extra whitespace</param>
    /// <returns>Processed HTML content in single-line format with normalized whitespace</returns>
    string ProcessHtmlContent(string htmlContent);

    /// <summary>
    /// Validates if the content is valid HTML
    /// </summary>
    /// <param name="htmlContent">HTML content to validate</param>
    /// <returns>True if valid HTML, false otherwise</returns>
    bool IsValidHtml(string htmlContent);
}
