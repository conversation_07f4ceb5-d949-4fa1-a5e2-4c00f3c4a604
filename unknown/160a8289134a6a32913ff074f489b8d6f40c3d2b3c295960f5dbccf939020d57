using Application.Resumes.Create;
using Application.Services;
using FluentValidation.TestHelper;

namespace Application.UnitTests.Resumes.Create;

public class CreateResumeCommandValidatorTests
{
    private readonly IHtmlContentProcessor _htmlContentProcessor;
    private readonly CreateResumeCommandValidator _validator;

    public CreateResumeCommandValidatorTests()
    {
        _htmlContentProcessor = Substitute.For<IHtmlContentProcessor>();
        _validator = new CreateResumeCommandValidator(_htmlContentProcessor);
    }

    [Fact]
    public void Validate_WithValidCommand_ShouldNotHaveValidationErrors()
    {
        // Arrange
        CreateResumeCommand command = new()
        {
            UserId = Guid.NewGuid(),
            ParentId = null,
            ResumeContent = "<html><body><h1>Valid HTML</h1></body></html>"
        };

        _htmlContentProcessor.IsValidHtml(command.ResumeContent).Returns(true);

        // Act
        TestValidationResult<CreateResumeCommand> result = _validator.TestValidate(command);

        // Assert
        result.ShouldNotHaveAnyValidationErrors();
    }

    [Fact]
    public void Validate_WithEmptyUserId_ShouldHaveValidationError()
    {
        // Arrange
        CreateResumeCommand command = new()
        {
            UserId = Guid.Empty,
            ResumeContent = "<html><body><h1>Valid HTML</h1></body></html>"
        };

        _htmlContentProcessor.IsValidHtml(command.ResumeContent).Returns(true);

        // Act
        TestValidationResult<CreateResumeCommand> result = _validator.TestValidate(command);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.UserId)
            .WithErrorMessage("User ID is required");
    }

    [Fact]
    public void Validate_WithEmptyResumeContent_ShouldHaveValidationError()
    {
        // Arrange
        CreateResumeCommand command = new()
        {
            UserId = Guid.NewGuid(),
            ResumeContent = ""
        };

        // Act
        TestValidationResult<CreateResumeCommand> result = _validator.TestValidate(command);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.ResumeContent)
            .WithErrorMessage("Resume content is required");
    }

    [Fact]
    public void Validate_WithInvalidHtmlContent_ShouldHaveValidationError()
    {
        // Arrange
        CreateResumeCommand command = new()
        {
            UserId = Guid.NewGuid(),
            ResumeContent = "Plain text without HTML tags"
        };

        _htmlContentProcessor.IsValidHtml(command.ResumeContent).Returns(false);

        // Act
        TestValidationResult<CreateResumeCommand> result = _validator.TestValidate(command);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.ResumeContent)
            .WithErrorMessage("Resume content must be valid HTML. Ensure it contains proper HTML tags and structure.");
    }

    [Fact]
    public void Validate_WithContentTooShort_ShouldHaveValidationError()
    {
        // Arrange
        CreateResumeCommand command = new()
        {
            UserId = Guid.NewGuid(),
            ResumeContent = "<p>Hi</p>" // Less than 10 characters
        };

        // Act
        TestValidationResult<CreateResumeCommand> result = _validator.TestValidate(command);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.ResumeContent)
            .WithErrorMessage("Resume content must be between 10 and 1.000.000 characters");
    }

    [Fact]
    public void Validate_WithContentTooLong_ShouldHaveValidationError()
    {
        // Arrange
        string longContent = new('a', 1_000_001); // More than 1MB
        CreateResumeCommand command = new()
        {
            UserId = Guid.NewGuid(),
            ResumeContent = longContent
        };

        // Act
        TestValidationResult<CreateResumeCommand> result = _validator.TestValidate(command);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.ResumeContent)
            .WithErrorMessage("Resume content must be between 10 and 1.000.000 characters");
    }

    [Fact]
    public void Validate_WithEmptyGuidParentId_ShouldHaveValidationError()
    {
        // Arrange
        CreateResumeCommand command = new()
        {
            UserId = Guid.NewGuid(),
            ParentId = Guid.Empty,
            ResumeContent = "<html><body><h1>Valid HTML</h1></body></html>"
        };

        _htmlContentProcessor.IsValidHtml(command.ResumeContent).Returns(true);

        // Act
        TestValidationResult<CreateResumeCommand> result = _validator.TestValidate(command);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.ParentId)
            .WithErrorMessage("Parent ID cannot be empty GUID");
    }

    [Fact]
    public void Validate_WithNullParentId_ShouldNotHaveValidationError()
    {
        // Arrange
        CreateResumeCommand command = new()
        {
            UserId = Guid.NewGuid(),
            ParentId = null,
            ResumeContent = "<html><body><h1>Valid HTML</h1></body></html>"
        };

        _htmlContentProcessor.IsValidHtml(command.ResumeContent).Returns(true);

        // Act
        TestValidationResult<CreateResumeCommand> result = _validator.TestValidate(command);

        // Assert
        result.ShouldNotHaveValidationErrorFor(x => x.ParentId);
    }

    [Fact]
    public void Validate_WithValidParentId_ShouldNotHaveValidationError()
    {
        // Arrange
        CreateResumeCommand command = new()
        {
            UserId = Guid.NewGuid(),
            ParentId = Guid.NewGuid(),
            ResumeContent = "<html><body><h1>Valid HTML</h1></body></html>"
        };

        _htmlContentProcessor.IsValidHtml(command.ResumeContent).Returns(true);

        // Act
        TestValidationResult<CreateResumeCommand> result = _validator.TestValidate(command);

        // Assert
        result.ShouldNotHaveValidationErrorFor(x => x.ParentId);
    }
}
