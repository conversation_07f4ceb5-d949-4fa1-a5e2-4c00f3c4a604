﻿using Application.Abstractions.Authentication;
using Application.Abstractions.Data;
using Application.Abstractions.Messaging;
using Application.Services;
using Domain.Resumes;
using Domain.Users;
using Microsoft.EntityFrameworkCore;
using SharedKernel;

namespace Application.Resumes.Create;

internal sealed class CreateResumeCommandHandler(
    IApplicationDbContext context,
    IUserContext userContext,
    IHtmlContentProcessor htmlContentProcessor)
    : ICommandHandler<CreateResumeCommand, Guid>
{
    public async Task<Result<Guid>> Handle(CreateResumeCommand command, CancellationToken cancellationToken)
    {
        if (userContext.UserId != command.UserId)
        {
            return Result.Failure<Guid>(UserErrors.Unauthorized());
        }

        User? user = await context.Users.AsNoTracking()
            .SingleOrDefaultAsync(u => u.Id == command.UserId, cancellationToken);

        if (user is null)
        {
            return Result.Failure<Guid>(UserErrors.NotFound(command.UserId));
        }

        // Business rule validation: Check parent resume constraints
        bool isCreatingParentResume = command.ParentId == null || command.ParentId == Guid.Empty;

        if (isCreatingParentResume)
        {
            // Check if user already has a parent resume
            bool hasParentResume = await context.Resumes
                .AnyAsync(r => r.UserId == command.UserId &&
                              (r.ParentId == null || r.ParentId == Guid.Empty) &&
                              !r.IsDeleted,
                         cancellationToken);

            if (hasParentResume)
            {
                return Result.Failure<Guid>(ResumeErrors.UserAlreadyHasParentResume(command.UserId));
            }
        }
        else
        {
            // Check if the specified parent resume exists and belongs to the user
            Guid parentId = command.ParentId!.Value;
            Resume? parentResume = await context.Resumes
                .AsNoTracking()
                .SingleOrDefaultAsync(r => r.Id == parentId &&
                                          r.UserId == command.UserId &&
                                          !r.IsDeleted,
                                     cancellationToken);

            if (parentResume is null)
            {
                return Result.Failure<Guid>(ResumeErrors.ParentResumeNotFound(parentId));
            }

            // Ensure the parent resume is actually a parent (not a child resume)
            if (parentResume.ParentId != null && parentResume.ParentId != Guid.Empty)
            {
                return Result.Failure<Guid>(ResumeErrors.InvalidParentResume(parentId, parentResume.ParentId.Value));
            }
        }

        // Process HTML content to normalize whitespace and format
        string processedContent;
        try
        {
            processedContent = htmlContentProcessor.ProcessHtmlContent(command.ResumeContent);

            // Validate the processed content is not empty
            if (string.IsNullOrWhiteSpace(processedContent))
            {
                return Result.Failure<Guid>(ResumeErrors.EmptyContent());
            }
        }
        catch (Exception)
        {
            // If HTML processing fails, return a validation error
            return Result.Failure<Guid>(ResumeErrors.InvalidContent());
        }

        var resume = new Resume
        {
            UserId = user.Id,
            ParentId = command.ParentId,
            ResumeContent = processedContent,
        };

        resume.Raise(new ResumeCreatedDomainEvent(resume.Id));

        context.Resumes.Add(resume);

        await context.SaveChangesAsync(cancellationToken);

        return resume.Id;
    }
}
