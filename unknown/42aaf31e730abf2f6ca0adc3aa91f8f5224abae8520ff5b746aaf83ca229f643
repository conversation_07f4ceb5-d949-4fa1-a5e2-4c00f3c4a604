﻿using Application.Services;
using FluentValidation;

namespace Application.Resumes.Create;

public class CreateResumeCommandValidator : AbstractValidator<CreateResumeCommand>
{
    private const int MaxContentLength = 1_000_000; // 1MB limit for HTML content
    private const int MinContentLength = 10; // Minimum content length

    private readonly IHtmlContentProcessor _htmlContentProcessor;

    public CreateResumeCommandValidator(IHtmlContentProcessor htmlContentProcessor)
    {
        _htmlContentProcessor = htmlContentProcessor;
        RuleFor(c => c.UserId)
            .NotEmpty()
            .WithMessage("User ID is required");

        RuleFor(c => c.ResumeContent)
            .NotEmpty()
            .WithMessage("Resume content is required")
            .Length(MinContentLength, MaxContentLength)
            .WithMessage($"Resume content must be between {MinContentLength} and {MaxContentLength:N0} characters")
            .Must(BeValidHtmlContent)
            .WithMessage("Resume content must be valid HTML. Ensure it contains proper HTML tags and structure.");

        RuleFor(c => c.ParentId)
            .NotEqual(Guid.Empty)
            .When(c => c.ParentId.HasValue)
            .WithMessage("Parent ID cannot be empty GUID");
    }

    private bool BeValidHtmlContent(string content)
    {
        return _htmlContentProcessor.IsValidHtml(content);
    }
}
