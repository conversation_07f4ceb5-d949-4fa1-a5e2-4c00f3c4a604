using System.Text.RegularExpressions;

namespace Application.Services;

/// <summary>
/// Service for processing and normalizing HTML content
/// </summary>
public class HtmlContentProcessor : IHtmlContentProcessor
{
    private static readonly Regex WhitespaceRegex = new(@"\s+", RegexOptions.Compiled);
    private static readonly Regex NewlineRegex = new(@"[\r\n]+", RegexOptions.Compiled);
    private static readonly Regex TagWhitespaceRegex = new(@">\s+<", RegexOptions.Compiled);
    private static readonly Regex HtmlTagRegex = new(@"<[^>]+>", RegexOptions.Compiled);

    public string ProcessHtmlContent(string htmlContent)
    {
        if (string.IsNullOrWhiteSpace(htmlContent))
        {
            return string.Empty;
        }

        try
        {
            // Step 1: Normalize line endings and remove excessive whitespace
            string processed = htmlContent.Trim();

            // Step 2: Replace multiple consecutive newlines with single spaces
            processed = NewlineRegex.Replace(processed, " ");

            // Step 3: Replace multiple consecutive whitespace characters with single spaces
            processed = WhitespaceRegex.Replace(processed, " ");

            // Step 4: Remove whitespace between HTML tags (><) to create cleaner single-line HTML
            processed = TagWhitespaceRegex.Replace(processed, "><");

            // Step 5: Final trim to remove any leading/trailing whitespace
            processed = processed.Trim();

            return processed;
        }
        catch (Exception)
        {
            // If processing fails, return the original content trimmed
            // This ensures we don't break the flow for edge cases
            return htmlContent.Trim();
        }
    }

    public bool IsValidHtml(string htmlContent)
    {
        if (string.IsNullOrWhiteSpace(htmlContent))
        {
            return false;
        }

        string trimmedContent = htmlContent.Trim();

        // Basic HTML validation - check for basic HTML structure
        // Must contain at least one opening and one closing tag
        bool hasOpeningTag = trimmedContent.Contains('<');
        bool hasClosingTag = trimmedContent.Contains('>');
        
        if (!hasOpeningTag || !hasClosingTag)
        {
            return false;
        }

        // Additional validation: Check for balanced tags (basic check)
        try
        {
            MatchCollection tags = HtmlTagRegex.Matches(trimmedContent);
            if (tags.Count == 0)
            {
                return false;
            }

            // Allow both full HTML documents and HTML fragments
            // This is a basic validation - for production, consider using a proper HTML parser
            return true;
        }
        catch
        {
            return false;
        }
    }
}
