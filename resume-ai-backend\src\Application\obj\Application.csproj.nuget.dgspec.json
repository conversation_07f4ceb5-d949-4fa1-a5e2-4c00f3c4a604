{"format": 1, "restore": {"C:\\Users\\<USER>\\OneDrive\\Desktop\\All\\Projects\\resume-ai\\resume-ai-backend\\src\\Application\\Application.csproj": {}}, "projects": {"C:\\Users\\<USER>\\OneDrive\\Desktop\\All\\Projects\\resume-ai\\resume-ai-backend\\src\\Application\\Application.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\OneDrive\\Desktop\\All\\Projects\\resume-ai\\resume-ai-backend\\src\\Application\\Application.csproj", "projectName": "Application", "projectPath": "C:\\Users\\<USER>\\OneDrive\\Desktop\\All\\Projects\\resume-ai\\resume-ai-backend\\src\\Application\\Application.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\OneDrive\\Desktop\\All\\Projects\\resume-ai\\resume-ai-backend\\src\\Application\\obj\\", "projectStyle": "PackageReference", "centralPackageVersionsManagementEnabled": true, "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"C:\\Users\\<USER>\\OneDrive\\Desktop\\All\\Projects\\resume-ai\\resume-ai-backend\\src\\Domain\\Domain.csproj": {"projectPath": "C:\\Users\\<USER>\\OneDrive\\Desktop\\All\\Projects\\resume-ai\\resume-ai-backend\\src\\Domain\\Domain.csproj"}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\All\\Projects\\resume-ai\\resume-ai-backend\\src\\SharedKernel\\SharedKernel.csproj": {"projectPath": "C:\\Users\\<USER>\\OneDrive\\Desktop\\All\\Projects\\resume-ai\\resume-ai-backend\\src\\SharedKernel\\SharedKernel.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"FluentValidation.DependencyInjectionExtensions": {"target": "Package", "version": "[12.0.0, )", "versionCentrallyManaged": true}, "Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[9.0.6, )", "versionCentrallyManaged": true}, "Microsoft.Extensions.Logging.Abstractions": {"target": "Package", "version": "[9.0.6, )", "versionCentrallyManaged": true}, "Scrutor": {"target": "Package", "version": "[6.1.0, )", "versionCentrallyManaged": true}, "SonarAnalyzer.CSharp": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[10.12.0.118525, )", "versionCentrallyManaged": true}}, "centralPackageVersions": {"Aspire.Hosting.AppHost": "9.3.1", "Aspire.Hosting.PostgreSQL": "9.3.1", "AspNetCore.HealthChecks.NpgSql": "9.0.0", "AspNetCore.HealthChecks.UI.Client": "9.0.0", "CommunityToolkit.Aspire.Hosting.Ollama": "9.4.0", "CommunityToolkit.Aspire.OllamaSharp": "9.5.0", "coverlet.collector": "6.0.4", "EFCore.NamingConventions": "9.0.0", "FluentAssertions": "7.0.0", "FluentValidation.DependencyInjectionExtensions": "12.0.0", "Microsoft.AspNetCore.Authentication.JwtBearer": "9.0.6", "Microsoft.AspNetCore.OpenApi": "9.0.6", "Microsoft.EntityFrameworkCore": "9.0.6", "Microsoft.EntityFrameworkCore.Tools": "9.0.6", "Microsoft.Extensions.AI": "9.6.0", "Microsoft.Extensions.Diagnostics.HealthChecks": "9.0.6", "Microsoft.Extensions.Http.Polly": "9.0.6", "Microsoft.Extensions.Http.Resilience": "9.6.0", "Microsoft.Extensions.Logging.Abstractions": "9.0.6", "Microsoft.Extensions.ServiceDiscovery": "9.3.1", "Microsoft.NET.Test.Sdk": "17.14.1", "Microsoft.VisualStudio.Azure.Containers.Tools.Targets": "1.21.2", "NetArchTest.Rules": "1.3.2", "Newtonsoft.Json": "13.0.3", "Npgsql.EntityFrameworkCore.PostgreSQL": "9.0.4", "Npgsql.OpenTelemetry": "9.0.3", "NSubstitute": "5.3.0", "OpenTelemetry.Exporter.OpenTelemetryProtocol": "1.12.0", "OpenTelemetry.Extensions.Hosting": "1.12.0", "OpenTelemetry.Instrumentation.AspNetCore": "1.12.0", "OpenTelemetry.Instrumentation.EntityFrameworkCore": "1.12.0-beta.2", "OpenTelemetry.Instrumentation.Http": "1.12.0", "OpenTelemetry.Instrumentation.Runtime": "1.12.0", "Polly": "8.5.0", "Polly.Extensions.Http": "3.0.0", "Scrutor": "6.1.0", "Shouldly": "4.3.0", "SonarAnalyzer.CSharp": "10.12.0.118525", "Swashbuckle.AspNetCore": "9.0.1", "xunit": "2.9.3", "xunit.runner.visualstudio": "3.1.1", "YamlDotNet": "16.2.1"}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\All\\Projects\\resume-ai\\resume-ai-backend\\src\\Domain\\Domain.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\OneDrive\\Desktop\\All\\Projects\\resume-ai\\resume-ai-backend\\src\\Domain\\Domain.csproj", "projectName": "Domain", "projectPath": "C:\\Users\\<USER>\\OneDrive\\Desktop\\All\\Projects\\resume-ai\\resume-ai-backend\\src\\Domain\\Domain.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\OneDrive\\Desktop\\All\\Projects\\resume-ai\\resume-ai-backend\\src\\Domain\\obj\\", "projectStyle": "PackageReference", "centralPackageVersionsManagementEnabled": true, "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"C:\\Users\\<USER>\\OneDrive\\Desktop\\All\\Projects\\resume-ai\\resume-ai-backend\\src\\SharedKernel\\SharedKernel.csproj": {"projectPath": "C:\\Users\\<USER>\\OneDrive\\Desktop\\All\\Projects\\resume-ai\\resume-ai-backend\\src\\SharedKernel\\SharedKernel.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"SonarAnalyzer.CSharp": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[10.12.0.118525, )", "versionCentrallyManaged": true}}, "centralPackageVersions": {"Aspire.Hosting.AppHost": "9.3.1", "Aspire.Hosting.PostgreSQL": "9.3.1", "AspNetCore.HealthChecks.NpgSql": "9.0.0", "AspNetCore.HealthChecks.UI.Client": "9.0.0", "CommunityToolkit.Aspire.Hosting.Ollama": "9.4.0", "CommunityToolkit.Aspire.OllamaSharp": "9.5.0", "coverlet.collector": "6.0.4", "EFCore.NamingConventions": "9.0.0", "FluentAssertions": "7.0.0", "FluentValidation.DependencyInjectionExtensions": "12.0.0", "Microsoft.AspNetCore.Authentication.JwtBearer": "9.0.6", "Microsoft.AspNetCore.OpenApi": "9.0.6", "Microsoft.EntityFrameworkCore": "9.0.6", "Microsoft.EntityFrameworkCore.Tools": "9.0.6", "Microsoft.Extensions.AI": "9.6.0", "Microsoft.Extensions.Diagnostics.HealthChecks": "9.0.6", "Microsoft.Extensions.Http.Polly": "9.0.6", "Microsoft.Extensions.Http.Resilience": "9.6.0", "Microsoft.Extensions.Logging.Abstractions": "9.0.6", "Microsoft.Extensions.ServiceDiscovery": "9.3.1", "Microsoft.NET.Test.Sdk": "17.14.1", "Microsoft.VisualStudio.Azure.Containers.Tools.Targets": "1.21.2", "NetArchTest.Rules": "1.3.2", "Newtonsoft.Json": "13.0.3", "Npgsql.EntityFrameworkCore.PostgreSQL": "9.0.4", "Npgsql.OpenTelemetry": "9.0.3", "NSubstitute": "5.3.0", "OpenTelemetry.Exporter.OpenTelemetryProtocol": "1.12.0", "OpenTelemetry.Extensions.Hosting": "1.12.0", "OpenTelemetry.Instrumentation.AspNetCore": "1.12.0", "OpenTelemetry.Instrumentation.EntityFrameworkCore": "1.12.0-beta.2", "OpenTelemetry.Instrumentation.Http": "1.12.0", "OpenTelemetry.Instrumentation.Runtime": "1.12.0", "Polly": "8.5.0", "Polly.Extensions.Http": "3.0.0", "Scrutor": "6.1.0", "Shouldly": "4.3.0", "SonarAnalyzer.CSharp": "10.12.0.118525", "Swashbuckle.AspNetCore": "9.0.1", "xunit": "2.9.3", "xunit.runner.visualstudio": "3.1.1", "YamlDotNet": "16.2.1"}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\All\\Projects\\resume-ai\\resume-ai-backend\\src\\SharedKernel\\SharedKernel.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\OneDrive\\Desktop\\All\\Projects\\resume-ai\\resume-ai-backend\\src\\SharedKernel\\SharedKernel.csproj", "projectName": "SharedKernel", "projectPath": "C:\\Users\\<USER>\\OneDrive\\Desktop\\All\\Projects\\resume-ai\\resume-ai-backend\\src\\SharedKernel\\SharedKernel.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\OneDrive\\Desktop\\All\\Projects\\resume-ai\\resume-ai-backend\\src\\SharedKernel\\obj\\", "projectStyle": "PackageReference", "centralPackageVersionsManagementEnabled": true, "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"SonarAnalyzer.CSharp": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[10.12.0.118525, )", "versionCentrallyManaged": true}}, "centralPackageVersions": {"Aspire.Hosting.AppHost": "9.3.1", "Aspire.Hosting.PostgreSQL": "9.3.1", "AspNetCore.HealthChecks.NpgSql": "9.0.0", "AspNetCore.HealthChecks.UI.Client": "9.0.0", "CommunityToolkit.Aspire.Hosting.Ollama": "9.4.0", "CommunityToolkit.Aspire.OllamaSharp": "9.5.0", "coverlet.collector": "6.0.4", "EFCore.NamingConventions": "9.0.0", "FluentAssertions": "7.0.0", "FluentValidation.DependencyInjectionExtensions": "12.0.0", "Microsoft.AspNetCore.Authentication.JwtBearer": "9.0.6", "Microsoft.AspNetCore.OpenApi": "9.0.6", "Microsoft.EntityFrameworkCore": "9.0.6", "Microsoft.EntityFrameworkCore.Tools": "9.0.6", "Microsoft.Extensions.AI": "9.6.0", "Microsoft.Extensions.Diagnostics.HealthChecks": "9.0.6", "Microsoft.Extensions.Http.Polly": "9.0.6", "Microsoft.Extensions.Http.Resilience": "9.6.0", "Microsoft.Extensions.Logging.Abstractions": "9.0.6", "Microsoft.Extensions.ServiceDiscovery": "9.3.1", "Microsoft.NET.Test.Sdk": "17.14.1", "Microsoft.VisualStudio.Azure.Containers.Tools.Targets": "1.21.2", "NetArchTest.Rules": "1.3.2", "Newtonsoft.Json": "13.0.3", "Npgsql.EntityFrameworkCore.PostgreSQL": "9.0.4", "Npgsql.OpenTelemetry": "9.0.3", "NSubstitute": "5.3.0", "OpenTelemetry.Exporter.OpenTelemetryProtocol": "1.12.0", "OpenTelemetry.Extensions.Hosting": "1.12.0", "OpenTelemetry.Instrumentation.AspNetCore": "1.12.0", "OpenTelemetry.Instrumentation.EntityFrameworkCore": "1.12.0-beta.2", "OpenTelemetry.Instrumentation.Http": "1.12.0", "OpenTelemetry.Instrumentation.Runtime": "1.12.0", "Polly": "8.5.0", "Polly.Extensions.Http": "3.0.0", "Scrutor": "6.1.0", "Shouldly": "4.3.0", "SonarAnalyzer.CSharp": "10.12.0.118525", "Swashbuckle.AspNetCore": "9.0.1", "xunit": "2.9.3", "xunit.runner.visualstudio": "3.1.1", "YamlDotNet": "16.2.1"}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}}}