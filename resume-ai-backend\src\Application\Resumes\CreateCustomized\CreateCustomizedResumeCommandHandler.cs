using Application.Abstractions.Data;
using Application.Abstractions.Messaging;
using Application.Services;
using Domain.Resumes;
using Microsoft.EntityFrameworkCore;
using SharedKernel;

namespace Application.Resumes.CreateCustomized;

internal sealed class CreateCustomizedResumeCommandHandler(
    IApplicationDbContext context,
    IHtmlContentProcessor htmlContentProcessor)
    : ICommandHandler<CreateCustomizedResumeCommand, Guid>
{
    public async Task<Result<Guid>> Handle(CreateCustomizedResumeCommand command, CancellationToken cancellationToken)
    {
        // Validate parent resume exists and belongs to user
        Resume? parentResume = await context.Resumes
            .AsNoTracking()
            .FirstOrDefaultAsync(r => r.Id == command.ParentResumeId && 
                                     r.UserId == command.UserId && 
                                     !r.IsDeleted, cancellationToken);

        if (parentResume is null)
        {
            return Result.Failure<Guid>(ResumeErrors.ParentResumeNotFound(command.ParentResumeId));
        }

        // Ensure the parent resume is actually a parent (not a child resume)
        if (parentResume.ParentId != null && parentResume.ParentId != Guid.Empty)
        {
            return Result.Failure<Guid>(ResumeErrors.InvalidParentResume(command.ParentResumeId, parentResume.ParentId.Value));
        }

        // Process HTML content to normalize whitespace and format
        string processedContent;
        try
        {
            processedContent = htmlContentProcessor.ProcessHtmlContent(command.CustomizedContent);

            // Validate the processed content is not empty
            if (string.IsNullOrWhiteSpace(processedContent))
            {
                return Result.Failure<Guid>(ResumeErrors.EmptyContent());
            }
        }
        catch (Exception)
        {
            // If HTML processing fails, return a validation error
            return Result.Failure<Guid>(ResumeErrors.InvalidContent());
        }

        var customizedResume = new Resume
        {
            UserId = command.UserId,
            ParentId = command.ParentResumeId,
            ResumeContent = processedContent
        };

        // Raise domain event for resume creation
        customizedResume.Raise(new ResumeCreatedDomainEvent(customizedResume.Id));

        // Raise domain event for AI customization completion
        customizedResume.Raise(new ResumeCustomizedByAIDomainEvent(
            customizedResume.Id, 
            command.ParentResumeId, 
            command.CustomizationSummary));

        context.Resumes.Add(customizedResume);
        await context.SaveChangesAsync(cancellationToken);

        return customizedResume.Id;
    }
}
